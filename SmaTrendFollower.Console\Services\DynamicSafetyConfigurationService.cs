using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Dynamic safety configuration service that adjusts safety parameters based on current account size
/// </summary>
public class DynamicSafetyConfigurationService : IDynamicSafetyConfigurationService
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<DynamicSafetyConfigurationService> _logger;
    private readonly ISafetyConfigurationService _baseSafetyService;

    public DynamicSafetyConfigurationService(
        IAlpacaClientFactory clientFactory,
        ILogger<DynamicSafetyConfigurationService> logger,
        ISafetyConfigurationService baseSafetyService)
    {
        _clientFactory = clientFactory;
        _logger = logger;
        _baseSafetyService = baseSafetyService;
    }

    /// <summary>
    /// Gets dynamic safety configuration based on current account equity
    /// </summary>
    public async Task<SafetyConfiguration> GetDynamicConfigurationAsync()
    {
        try
        {
            var rateLimitHelper = _clientFactory.GetRateLimitHelper();
            
            return await rateLimitHelper.ExecuteAsync(async () =>
            {
                using var tradingClient = _clientFactory.CreateTradingClient();
                var account = await tradingClient.GetAccountAsync();
                var equity = account.Equity ?? 0m;

                var dynamicConfig = CalculateDynamicSafetyParameters(equity);
                
                _logger.LogInformation("Dynamic safety configuration calculated for equity {Equity:C}: " +
                                     "MaxDailyLoss={MaxDailyLoss:C}, MaxPositions={MaxPositions}, " +
                                     "MaxSingleTrade={MaxSingleTrade:C}, MaxPositionSize={MaxPositionSize:P2}",
                    equity, dynamicConfig.MaxDailyLoss, dynamicConfig.MaxPositions, 
                    dynamicConfig.MaxSingleTradeValue, dynamicConfig.MaxPositionSizePercent);

                return dynamicConfig;
            }, "GetDynamicConfiguration");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dynamic safety configuration, falling back to base configuration");
            return _baseSafetyService.LoadConfiguration();
        }
    }

    /// <summary>
    /// Calculates dynamic safety parameters based on account equity
    /// </summary>
    private SafetyConfiguration CalculateDynamicSafetyParameters(decimal equity)
    {
        // Get base configuration from environment variables
        var baseConfig = _baseSafetyService.LoadConfiguration();

        // Calculate dynamic parameters based on account size
        var (maxDailyLoss, maxPositions, maxSingleTrade, maxPositionPercent, minEquity) = equity switch
        {
            // Very small accounts (under $5k)
            < 5000m => (
                Math.Max(equity * 0.02m, 25m),     // 2% daily loss, min $25
                3,                                  // Max 3 positions
                Math.Max(equity * 0.15m, 100m),    // 15% single trade, min $100
                0.08m,                              // 8% max position size
                1000m                               // $1k minimum equity
            ),
            
            // Small accounts ($5k - $15k)
            < 15000m => (
                Math.Max(equity * 0.015m, 50m),    // 1.5% daily loss, min $50
                4,                                  // Max 4 positions
                Math.Max(equity * 0.12m, 200m),    // 12% single trade, min $200
                0.06m,                              // 6% max position size
                2000m                               // $2k minimum equity
            ),
            
            // Medium accounts ($15k - $50k)
            < 50000m => (
                Math.Max(equity * 0.012m, 100m),   // 1.2% daily loss, min $100
                5,                                  // Max 5 positions
                Math.Max(equity * 0.10m, 500m),    // 10% single trade, min $500
                0.05m,                              // 5% max position size
                3000m                               // $3k minimum equity
            ),
            
            // Large accounts ($50k - $100k)
            < 100000m => (
                Math.Max(equity * 0.010m, 200m),   // 1.0% daily loss, min $200
                6,                                  // Max 6 positions
                Math.Max(equity * 0.08m, 1000m),   // 8% single trade, min $1k
                0.04m,                              // 4% max position size
                5000m                               // $5k minimum equity
            ),
            
            // Very large accounts ($100k - $500k)
            < 500000m => (
                Math.Max(equity * 0.008m, 500m),   // 0.8% daily loss, min $500
                8,                                  // Max 8 positions
                Math.Max(equity * 0.06m, 2000m),   // 6% single trade, min $2k
                0.03m,                              // 3% max position size
                10000m                              // $10k minimum equity
            ),
            
            // Institutional accounts ($500k+)
            _ => (
                Math.Max(equity * 0.006m, 1000m),  // 0.6% daily loss, min $1k
                10,                                 // Max 10 positions
                Math.Max(equity * 0.05m, 5000m),   // 5% single trade, min $5k
                0.025m,                             // 2.5% max position size
                25000m                              // $25k minimum equity
            )
        };

        // Create dynamic configuration preserving environment-specific settings
        return baseConfig with
        {
            MaxDailyLoss = maxDailyLoss,
            MaxPositions = maxPositions,
            MaxSingleTradeValue = maxSingleTrade,
            MaxPositionSizePercent = maxPositionPercent,
            MinAccountEquity = Math.Min(minEquity, equity * 0.1m), // Never require more than 10% of current equity
            
            // Preserve environment-specific settings
            AllowedEnvironment = baseConfig.AllowedEnvironment,
            RequireConfirmation = baseConfig.RequireConfirmation,
            DryRunMode = baseConfig.DryRunMode,
            
            // Scale daily trades based on account size
            MaxDailyTrades = equity switch
            {
                < 10000m => 5,      // Small accounts: fewer trades
                < 50000m => 10,     // Medium accounts: moderate trades
                < 100000m => 15,    // Large accounts: more trades
                _ => 20             // Very large accounts: maximum trades
            }
        };
    }

    /// <summary>
    /// Gets account size tier for logging and monitoring
    /// </summary>
    public async Task<AccountSizeTier> GetAccountSizeTierAsync()
    {
        try
        {
            var rateLimitHelper = _clientFactory.GetRateLimitHelper();
            
            return await rateLimitHelper.ExecuteAsync(async () =>
            {
                using var tradingClient = _clientFactory.CreateTradingClient();
                var account = await tradingClient.GetAccountAsync();
                var equity = account.Equity ?? 0m;

                return equity switch
                {
                    < 5000m => AccountSizeTier.VerySmall,
                    < 15000m => AccountSizeTier.Small,
                    < 50000m => AccountSizeTier.Medium,
                    < 100000m => AccountSizeTier.Large,
                    < 500000m => AccountSizeTier.VeryLarge,
                    _ => AccountSizeTier.Institutional
                };
            }, "GetAccountSizeTier");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining account size tier");
            return AccountSizeTier.Small; // Safe default
        }
    }

    /// <summary>
    /// Gets risk summary for the current account
    /// </summary>
    public async Task<AccountRiskSummary> GetAccountRiskSummaryAsync()
    {
        try
        {
            var config = await GetDynamicConfigurationAsync();
            var tier = await GetAccountSizeTierAsync();
            
            var rateLimitHelper = _clientFactory.GetRateLimitHelper();
            
            return await rateLimitHelper.ExecuteAsync(async () =>
            {
                using var tradingClient = _clientFactory.CreateTradingClient();
                var account = await tradingClient.GetAccountAsync();
                var equity = account.Equity ?? 0m;

                return new AccountRiskSummary(
                    equity,
                    tier,
                    config.MaxDailyLoss,
                    config.MaxSingleTradeValue,
                    config.MaxPositionSizePercent,
                    config.MaxPositions,
                    config.MaxDailyTrades,
                    config.MaxDailyLoss / equity, // Daily loss as percentage
                    config.MaxSingleTradeValue / equity // Single trade as percentage
                );
            }, "GetAccountRiskSummary");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting account risk summary");
            throw;
        }
    }
}

/// <summary>
/// Account size tiers for risk management
/// </summary>
public enum AccountSizeTier
{
    VerySmall,      // < $5k
    Small,          // $5k - $15k
    Medium,         // $15k - $50k
    Large,          // $50k - $100k
    VeryLarge,      // $100k - $500k
    Institutional   // $500k+
}

/// <summary>
/// Account risk summary
/// </summary>
public record AccountRiskSummary(
    decimal AccountEquity,
    AccountSizeTier SizeTier,
    decimal MaxDailyLoss,
    decimal MaxSingleTradeValue,
    decimal MaxPositionSizePercent,
    int MaxPositions,
    int MaxDailyTrades,
    decimal DailyLossPercent,
    decimal SingleTradePercent
);
