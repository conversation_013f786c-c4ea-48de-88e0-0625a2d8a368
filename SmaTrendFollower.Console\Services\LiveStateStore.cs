using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Redis-based implementation of live trading state storage.
/// Manages fast, transient data like trailing stops, signal flags, and retry queues.
/// </summary>
public sealed class LiveStateStore : ILiveStateStore, IDisposable
{
    private readonly IDatabase _database;
    private readonly IConnectionMultiplexer _redis;
    private readonly ILogger<LiveStateStore> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    // Redis key prefixes for different data types
    private const string StopPrefix = "stop:";
    private const string SignalPrefix = "signal:";
    private const string PositionPrefix = "position:";
    private const string RetryQueue = "retry_queue";
    private const string MarketStatePrefix = "market:";

    public LiveStateStore(IConfiguration configuration, ILogger<LiveStateStore> logger)
    {
        _logger = logger;
        
        var redisUrl = configuration["REDIS_URL"] ?? "localhost:6379";
        var redisPassword = configuration["REDIS_PASSWORD"];
        var redisDatabase = int.Parse(configuration["REDIS_DATABASE"] ?? "0");

        var configOptions = ConfigurationOptions.Parse(redisUrl);
        if (!string.IsNullOrEmpty(redisPassword))
        {
            configOptions.Password = redisPassword;
        }
        
        configOptions.AbortOnConnectFail = false;
        configOptions.ConnectRetry = 3;
        configOptions.ConnectTimeout = 5000;
        configOptions.SyncTimeout = 5000;

        _redis = ConnectionMultiplexer.Connect(configOptions);
        _database = _redis.GetDatabase(redisDatabase);

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };

        _logger.LogInformation("Connected to Redis at {RedisUrl}, Database {Database}", redisUrl, redisDatabase);
    }

    public async Task SetTrailingStopAsync(string symbol, decimal stopPrice, CancellationToken cancellationToken = default)
    {
        try
        {
            var key = StopPrefix + symbol;
            await _database.StringSetAsync(key, stopPrice.ToString("F4"));
            _logger.LogDebug("Set trailing stop for {Symbol}: {StopPrice:F4}", symbol, stopPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set trailing stop for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<decimal?> GetTrailingStopAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            var key = StopPrefix + symbol;
            var value = await _database.StringGetAsync(key);
            
            if (value.HasValue && decimal.TryParse(value, out var stopPrice))
            {
                return stopPrice;
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get trailing stop for {Symbol}", symbol);
            return null;
        }
    }

    public async Task RemoveTrailingStopAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            var key = StopPrefix + symbol;
            await _database.KeyDeleteAsync(key);
            _logger.LogDebug("Removed trailing stop for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove trailing stop for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<Dictionary<string, decimal>> GetAllTrailingStopsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            var keys = server.Keys(pattern: StopPrefix + "*").ToArray();
            
            if (!keys.Any())
                return new Dictionary<string, decimal>();

            var values = await _database.StringGetAsync(keys);
            var result = new Dictionary<string, decimal>();

            for (int i = 0; i < keys.Length; i++)
            {
                if (values[i].HasValue && decimal.TryParse(values[i], out var stopPrice))
                {
                    var symbol = keys[i].ToString().Substring(StopPrefix.Length);
                    result[symbol] = stopPrice;
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all trailing stops");
            return new Dictionary<string, decimal>();
        }
    }

    public async Task FlagSignalAsync(string symbol, DateTime date, CancellationToken cancellationToken = default)
    {
        try
        {
            var key = $"{SignalPrefix}{symbol}:{date:yyyyMMdd}";
            await _database.StringSetAsync(key, "1", TimeSpan.FromDays(1));
            _logger.LogDebug("Flagged signal for {Symbol} on {Date:yyyy-MM-dd}", symbol, date);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to flag signal for {Symbol} on {Date:yyyy-MM-dd}", symbol, date);
            throw;
        }
    }

    public async Task<bool> WasSignaledAsync(string symbol, DateTime date, CancellationToken cancellationToken = default)
    {
        try
        {
            var key = $"{SignalPrefix}{symbol}:{date:yyyyMMdd}";
            return await _database.KeyExistsAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check signal flag for {Symbol} on {Date:yyyy-MM-dd}", symbol, date);
            return false;
        }
    }

    public async Task ClearSignalFlagsAsync(DateTime olderThan, CancellationToken cancellationToken = default)
    {
        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            var pattern = SignalPrefix + "*";
            var keys = server.Keys(pattern: pattern).ToArray();
            
            var keysToDelete = new List<RedisKey>();
            
            foreach (var key in keys)
            {
                var keyStr = key.ToString();
                var datePart = keyStr.Substring(keyStr.LastIndexOf(':') + 1);
                
                if (DateTime.TryParseExact(datePart, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var keyDate))
                {
                    if (keyDate < olderThan)
                    {
                        keysToDelete.Add(key);
                    }
                }
            }

            if (keysToDelete.Any())
            {
                await _database.KeyDeleteAsync(keysToDelete.ToArray());
                _logger.LogDebug("Cleared {Count} old signal flags", keysToDelete.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear old signal flags");
            throw;
        }
    }

    public async Task SetPositionStateAsync(string symbol, PositionState state, CancellationToken cancellationToken = default)
    {
        try
        {
            var key = PositionPrefix + symbol;
            var json = JsonSerializer.Serialize(state, _jsonOptions);
            await _database.StringSetAsync(key, json);
            _logger.LogDebug("Set position state for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set position state for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<PositionState?> GetPositionStateAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            var key = PositionPrefix + symbol;
            var json = await _database.StringGetAsync(key);
            
            if (json.HasValue && !string.IsNullOrEmpty(json))
            {
                return JsonSerializer.Deserialize<PositionState>(json!, _jsonOptions);
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get position state for {Symbol}", symbol);
            return null;
        }
    }

    public async Task RemovePositionStateAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            var key = PositionPrefix + symbol;
            await _database.KeyDeleteAsync(key);
            _logger.LogDebug("Removed position state for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove position state for {Symbol}", symbol);
            throw;
        }
    }

    public async Task EnqueueRetryAsync(RetryItem item, CancellationToken cancellationToken = default)
    {
        try
        {
            var json = JsonSerializer.Serialize(item, _jsonOptions);
            await _database.ListLeftPushAsync(RetryQueue, json);
            _logger.LogDebug("Enqueued retry item: {Operation}", item.Operation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to enqueue retry item");
            throw;
        }
    }

    public async Task<RetryItem?> DequeueRetryAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var json = await _database.ListRightPopAsync(RetryQueue);
            
            if (json.HasValue && !string.IsNullOrEmpty(json))
            {
                return JsonSerializer.Deserialize<RetryItem>(json!, _jsonOptions);
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to dequeue retry item");
            return null;
        }
    }

    public async Task<int> GetRetryQueueLengthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return (int)await _database.ListLengthAsync(RetryQueue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get retry queue length");
            return 0;
        }
    }

    public async Task SetMarketStateAsync(string key, object value, TimeSpan expiry, CancellationToken cancellationToken = default)
    {
        try
        {
            var redisKey = MarketStatePrefix + key;
            var json = JsonSerializer.Serialize(value, _jsonOptions);
            await _database.StringSetAsync(redisKey, json, expiry);
            _logger.LogDebug("Set market state: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set market state: {Key}", key);
            throw;
        }
    }

    public async Task<T?> GetMarketStateAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var redisKey = MarketStatePrefix + key;
            var json = await _database.StringGetAsync(redisKey);
            
            if (json.HasValue && !string.IsNullOrEmpty(json))
            {
                return JsonSerializer.Deserialize<T>(json!, _jsonOptions);
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get market state: {Key}", key);
            return null;
        }
    }

    public async Task RemoveMarketStateAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var redisKey = MarketStatePrefix + key;
            await _database.KeyDeleteAsync(redisKey);
            _logger.LogDebug("Removed market state: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove market state: {Key}", key);
            throw;
        }
    }

    public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await _database.PingAsync();
            return _redis.IsConnected;
        }
        catch
        {
            return false;
        }
    }

    public async Task<LiveStateStats> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            
            var stopCount = server.Keys(pattern: StopPrefix + "*").Count();
            var signalCount = server.Keys(pattern: SignalPrefix + "*").Count();
            var positionCount = server.Keys(pattern: PositionPrefix + "*").Count();
            var marketStateCount = server.Keys(pattern: MarketStatePrefix + "*").Count();
            var retryQueueLength = await GetRetryQueueLengthAsync(cancellationToken);

            return new LiveStateStats(
                stopCount,
                signalCount,
                positionCount,
                retryQueueLength,
                marketStateCount,
                DateTime.UtcNow
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get live state stats");
            return new LiveStateStats(0, 0, 0, 0, 0, DateTime.UtcNow);
        }
    }

    public async Task FlushAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            await server.FlushDatabaseAsync();
            _logger.LogWarning("Flushed all Redis data");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to flush Redis data");
            throw;
        }
    }

    // Additional methods for future phases would go here

    public void Dispose()
    {
        _redis?.Dispose();
    }
}
