#############################################
#  SmaTrendFollower – LIVE TRADING CONFIG  #
#  ⚠️  LIVE TRADING WITH REAL MONEY ⚠️     #
#############################################

# ----- Alpaca Live Trading API -----
# ⚠️  REPLACE WITH YOUR LIVE ALPACA CREDENTIALS ⚠️
APCA_API_KEY_ID=REPLACE_WITH_LIVE_ALPACA_KEY_ID
APCA_API_SECRET_KEY=REPLACE_WITH_LIVE_ALPACA_SECRET_KEY
APCA_API_ENV=live

# ----- Polygon API (Market Data) -----
POLY_API_KEY=********************************

# ----- Discord Notifications -----
DISCORD_BOT_TOKEN=MTM4NTA1OTI3MDMzNjMxNTQ1NA.GtTKUd.fuCC2ZI-H-tTLZl41YF3gZj-w3gPbv_Xep8NoE
DISCORD_CHANNEL_ID=1385057459814797383

# ----- CONSERVATIVE LIVE TRADING SAFETY LIMITS -----
# Environment: Live trading only
SAFETY_ALLOWED_ENVIRONMENT=Live

# Risk Limits (Conservative for Live Trading)
SAFETY_MAX_DAILY_LOSS=120                    # $120 max daily loss
SAFETY_MAX_POSITIONS=5                       # Maximum 5 concurrent positions
SAFETY_MAX_SINGLE_TRADE_VALUE=1200          # $1,200 max per trade
SAFETY_MIN_ACCOUNT_EQUITY=5000               # $5,000 minimum account size
SAFETY_MAX_POSITION_SIZE_PERCENT=0.03        # 3% max position size
SAFETY_MAX_DAILY_TRADES=15                   # Maximum 15 trades per day

# Operational Controls
SAFETY_REQUIRE_CONFIRMATION=true             # Require explicit confirmation
SAFETY_DRY_RUN_MODE=false                    # Live trading mode

# ----- Redis Configuration (Recommended) -----
REDIS_URL=localhost:6379
REDIS_DATABASE=0

# ----- Strategy Configuration -----
# Universe and Signal Generation
UNIVERSE_SIZE=500                            # Screen top 500 stocks
TOP_N_SYMBOLS=5                              # Trade only top 5 signals (concentrated)

# Risk Management
VIX_THRESHOLD=25.0                           # Reduce positions when VIX > 25
MAX_POSITION_SIZE_PERCENT=0.03               # 3% max position size
MAX_DAILY_LOSS=120                           # $120 daily loss limit
MIN_ACCOUNT_EQUITY=5000                      # $5,000 minimum account

# Market Regime Filters
ENABLE_REGIME_FILTER=true                    # Block trading in volatile markets
ENABLE_VOLATILITY_FILTER=true               # Filter high volatility stocks

# ----- Options Strategies (Conservative) -----
ENABLE_OPTIONS_OVERLAY=false                 # Disable options for initial live trading
ENABLE_PROTECTIVE_PUTS=false                 # Disable until proven in paper
ENABLE_COVERED_CALLS=false                   # Disable until proven in paper

# ----- Logging and Monitoring -----
# Enhanced logging for live trading
LOG_LEVEL=Information
ENABLE_PERFORMANCE_LOGGING=true
ENABLE_TRADE_LOGGING=true
ENABLE_SAFETY_LOGGING=true

# ----- Timezone -----
BOT_TIMEZONE=America/New_York

#############################################
#  LIVE TRADING DEPLOYMENT CHECKLIST       #
#############################################
# Before using this configuration:
# 1. ✅ Replace APCA_API_KEY_ID with live key
# 2. ✅ Replace APCA_API_SECRET_KEY with live secret
# 3. ✅ Verify account has minimum $5,000 equity
# 4. ✅ Test connectivity: dotnet run -- --dry-run account-status
# 5. ✅ Validate configuration: dotnet run -- validate
# 6. ✅ Run safety tests: dotnet run -- --show-safety
# 7. ✅ Start with: dotnet run -- --confirm run
#############################################
