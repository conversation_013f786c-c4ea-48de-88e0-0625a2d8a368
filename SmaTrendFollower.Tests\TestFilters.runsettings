<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
  <RunConfiguration>
    <!-- Maximum time allowed for test run -->
    <TestSessionTimeout>600000</TestSessionTimeout>
    <!-- Maximum number of parallel test workers -->
    <MaxCpuCount>4</MaxCpuCount>
    <!-- Collect code coverage -->
    <CollectSourceInformation>true</CollectSourceInformation>
  </RunConfiguration>

  <TestRunParameters>
    <!-- Test environment parameters -->
    <Parameter name="Environment" value="Test" />
    <Parameter name="EnableIntegrationTests" value="false" />
  </TestRunParameters>

  <!-- Data collectors for code coverage -->
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="XPlat code coverage">
        <Configuration>
          <Format>cobertura</Format>
          <Exclude>
            [*]*.Tests.*
            [*]*Test*
            [*]*Mock*
          </Exclude>
        </Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>

  <!-- Test filters for different environments -->
  <MSTest>
    <Parallel>
      <Workers>4</Workers>
      <Scope>method</Scope>
    </Parallel>
  </MSTest>

  <!-- Logger configuration -->
  <LoggerRunSettings>
    <Loggers>
      <Logger friendlyName="console" enabled="True">
        <Configuration>
          <Verbosity>normal</Verbosity>
        </Configuration>
      </Logger>
      <Logger friendlyName="trx" enabled="True">
        <Configuration>
          <LogFileName>TestResults.trx</LogFileName>
        </Configuration>
      </Logger>
    </Loggers>
  </LoggerRunSettings>
</RunSettings>
