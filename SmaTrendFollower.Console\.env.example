#############################################
#  SmaTrendFollower – ENV TEMPLATE         #
#  Do NOT commit real keys.                #
#############################################

# ----- <PERSON><PERSON><PERSON> credentials -----
APCA_API_KEY_ID=REPLACE_ME
APCA_API_SECRET_KEY=REPLACE_ME
APCA_API_ENV=paper          # or live
# APCA_API_BASE_URL=https://paper-api.alpaca.markets  # optional

# ----- Polygon credentials -----
POLY_API_KEY=********************************

# ----- Bot settings ----------
BOT_TIMEZONE=America/New_York

# ----- Strategy settings -----
# Default universe size for screening
UNIVERSE_SIZE=500
# Top N symbols to trade (default 10)
TOP_N_SYMBOLS=10
# VIX threshold for position size throttling
VIX_THRESHOLD=25.0
# Enable options overlay strategies
ENABLE_OPTIONS_OVERLAY=true
# Enable protective puts
ENABLE_PROTECTIVE_PUTS=true
# Enable covered calls
ENABLE_COVERED_CALLS=true

# ----- Discord webhook (optional) -----
DISCORD_WEBHOOK_URL=REPLACE_ME

# ----- Redis configuration -----
# Redis connection string for cache warming and live trading state
REDIS_URL=localhost:6379
# Redis database number (default: 0)
REDIS_DATABASE=0
# Redis password (if required)
# REDIS_PASSWORD=your_redis_password

# ----- Risk management -----
# Maximum position size as percentage of account (default 10%)
MAX_POSITION_SIZE_PERCENT=0.10
# Maximum daily loss threshold (default $5000)
MAX_DAILY_LOSS=5000
# Minimum account equity for trading (default $25000 for PDT)
MIN_ACCOUNT_EQUITY=25000
